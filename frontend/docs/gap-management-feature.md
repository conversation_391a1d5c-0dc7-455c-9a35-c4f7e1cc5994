# 时间线元素间隙管理功能

## 功能概述

时间线元素间隙管理功能允许用户自动检测和删除时间线轨道中元素之间的时间间隙，提供更高效的时间线编辑体验。

## 功能特性

### 1. 自动间隙检测

- **起始间隙检测**：自动检测第一个元素之前的空白时间（从时间 0 开始）
- **元素间间隙检测**：检测相邻元素之间的时间间隙
- **最小间隙阈值**：只显示大于 100ms 的间隙，避免显示过小的间隙

### 2. 可视化指示器

- **按需显示**：间隙指示器只在鼠标悬停时显示，保持界面清洁
- **间隙高亮**：悬停时间隙区域以橙色虚线边框显示
- **时长显示**：悬停时显示间隙的具体时长
- **删除按钮**：悬停时显示红色删除按钮
- **平滑过渡**：所有视觉效果都有平滑的过渡动画

### 3. 间隙删除功能

- **一键删除**：点击删除按钮即可删除间隙
- **自动调整**：删除间隙后，后续所有元素自动向前移动
- **保持时长**：元素的 duration 保持不变，只调整时间位置
- **撤销支持**：支持撤销/重做操作

## 技术实现

### 核心组件

#### 1. GapIndicator 组件

```typescript
interface GapIndicatorProps {
  gap: TimeGap;
  containerWidth: number;
  timelineDisplayDuration: number;
  timelinePanOffsetX: number;
  onDeleteGap: (gap: TimeGap) => void;
}
```

#### 2. TimeGap 类型定义

```typescript
export type TimeGap = {
  id: string;
  startTime: number;
  endTime: number;
  duration: number;
  trackId: string;
  beforeElementId?: string;
  afterElementId?: string;
};
```

### 核心方法

#### 1. 间隙检测 - `detectTimeGaps(trackId: string)`

- 获取轨道中按时间排序的所有元素
- 检测第一个元素之前的间隙
- 检测相邻元素之间的间隙
- 返回间隙数组

#### 2. 间隙删除 - `deleteTimeGap(gap: TimeGap)`

- 开始历史分组操作
- 找到需要移动的元素（间隙结束时间之后的所有元素）
- 向前移动所有后续元素
- 更新最大时间并保存更改

## 使用方法

### 1. 查看间隙

1. 在时间线轨道中添加多个元素
2. 确保元素之间有时间间隙
3. 将鼠标悬停在轨道的空白区域上
4. 间隙会以橙色虚线边框显示（只在悬停时显示）

### 2. 删除间隙

1. 将鼠标悬停在间隙区域上
2. 查看间隙时长信息
3. 点击红色删除按钮
4. 间隙被删除，后续元素自动向前移动

### 3. 撤销操作

- 使用 Ctrl+Z 可以撤销间隙删除操作
- 使用 Ctrl+Y 可以重做间隙删除操作

## 性能优化

### 1. 智能检测

- 只在元素发生变化时重新检测间隙
- 使用 useMemo 缓存间隙计算结果

### 2. 渲染优化

- 间隙指示器只在可视区域内渲染
- 使用 CSS transform 进行高性能动画

### 3. 批量操作

- 间隙删除操作使用历史分组，多个相关操作视为一个撤销单元

## 注意事项

### 1. 最小间隙阈值

- 小于 100ms 的间隙不会显示指示器
- 这避免了显示过多的微小间隙

### 2. 时间边界

- 删除间隙时确保元素不会移动到负时间
- 保证元素的结束时间不小于其持续时间

### 3. 轨道隔离

- 间隙检测和删除只影响当前轨道
- 不会影响其他轨道的元素

## 扩展功能

### 未来可能的增强

1. **批量间隙删除**：一次删除轨道中的所有间隙
2. **间隙填充**：用静态内容填充间隙
3. **间隙调整**：手动调整间隙大小
4. **间隙统计**：显示轨道中间隙的总时长

## 兼容性

- 支持所有类型的时间线元素（视频、音频、图片、文本、GIF 等）
- 与现有的拖拽、吸附、重叠检测功能完全兼容
- 支持撤销/重做系统
- 响应式设计，适配不同屏幕尺寸
