"use client";
import React, { useState, useCallback, useMemo } from "react";
import { Box, IconButton, Tooltip } from "@mui/material";
import { Delete as DeleteIcon } from "@mui/icons-material";
import { observer } from "mobx-react";

interface TimeGap {
  id: string;
  startTime: number;
  endTime: number;
  duration: number;
  trackId: string;
  beforeElementId?: string;
  afterElementId?: string;
}

interface GapIndicatorProps {
  gap: TimeGap;
  containerWidth: number;
  timelineDisplayDuration: number;
  timelinePanOffsetX: number;
  onDeleteGap: (gap: TimeGap) => void;
}

/**
 * 计算时间线位置的工具函数
 */
const calculateTimelinePosition = (
  time: number,
  displayDuration: number,
  offsetX: number,
  containerWidth: number
): number => {
  const relativeTime = time - offsetX;
  return (relativeTime / displayDuration) * 100;
};

export const GapIndicator: React.FC<GapIndicatorProps> = observer(
  ({
    gap,
    containerWidth,
    timelineDisplayDuration,
    timelinePanOffsetX,
    onDeleteGap,
  }) => {
    const [isHovered, setIsHovered] = useState(false);

    // 计算间隙的位置和宽度
    const positionStyles = useMemo(() => {
      if (!containerWidth) {
        return {
          width: "0%",
          left: "0%",
          display: "none",
        };
      }

      const width =
        ((gap.endTime - gap.startTime) / timelineDisplayDuration) * 100;
      const left = calculateTimelinePosition(
        gap.startTime,
        timelineDisplayDuration,
        timelinePanOffsetX,
        containerWidth
      );

      // 如果间隙在可视区域外，不显示
      if (left > 100 || left + width < 0) {
        return {
          width: "0%",
          left: "0%",
          display: "none",
        };
      }

      return {
        width: `${Math.max(width, 0)}%`,
        left: `${Math.max(left, 0)}%`,
        display: "block",
      };
    }, [
      gap.startTime,
      gap.endTime,
      timelineDisplayDuration,
      timelinePanOffsetX,
      containerWidth,
    ]);

    // 处理删除间隙
    const handleDeleteGap = useCallback(
      (event: React.MouseEvent) => {
        event.preventDefault();
        event.stopPropagation();
        onDeleteGap(gap);
      },
      [gap, onDeleteGap]
    );

    // 处理鼠标悬停
    const handleMouseEnter = useCallback(() => {
      setIsHovered(true);
    }, []);

    const handleMouseLeave = useCallback(() => {
      setIsHovered(false);
    }, []);

    // 格式化时间显示
    const formatDuration = useCallback((duration: number) => {
      const seconds = Math.round((duration / 1000) * 10) / 10;
      return `${seconds}s`;
    }, []);

    // 如果间隙太小（小于100ms），不显示
    if (gap.duration < 100) {
      return null;
    }

    return (
      <Box
        sx={{
          position: "absolute",
          top: 0,
          height: "100%",
          ...positionStyles,
          backgroundColor: isHovered ? "rgba(255, 152, 0, 0.2)" : "transparent", // 默认透明，不显示
          border: isHovered ? "1px dashed rgba(255, 152, 0, 0.8)" : "none", // 默认无边框
          borderRadius: "2px",
          cursor: isHovered ? "pointer" : "default",
          transition: "all 0.2s ease",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minWidth: "20px",
          zIndex: 10,
          "&:hover": {
            backgroundColor: "rgba(255, 152, 0, 0.3)",
            border: "1px dashed rgba(255, 152, 0, 1)",
            cursor: "pointer",
          },
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* 间隙时长显示 */}
        {isHovered && (
          <Box
            sx={{
              position: "absolute",
              top: "-28px",
              left: "50%",
              transform: "translateX(-50%)",
              backgroundColor: "rgba(60, 60, 60, 0.95)",
              color: "white",
              padding: "4px 8px",
              borderRadius: "6px",
              fontSize: "11px",
              fontWeight: 500,
              whiteSpace: "nowrap",
              zIndex: 20,
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.3)",
              border: "1px solid rgba(100, 100, 100, 0.3)",
              "&::after": {
                content: '""',
                position: "absolute",
                top: "100%",
                left: "50%",
                transform: "translateX(-50%)",
                width: 0,
                height: 0,
                borderLeft: "4px solid transparent",
                borderRight: "4px solid transparent",
                borderTop: "4px solid rgba(60, 60, 60, 0.95)",
              },
            }}
          >
            {formatDuration(gap.duration)}
          </Box>
        )}

        {/* 删除按钮 */}
        {isHovered && (
          <Tooltip title="删除间隙" placement="top">
            <IconButton
              size="small"
              onClick={handleDeleteGap}
              sx={{
                width: "16px",
                height: "16px",
                backgroundColor: "rgba(244, 67, 54, 0.9)",
                color: "white",
                "&:hover": {
                  backgroundColor: "rgba(244, 67, 54, 1)",
                },
                "& .MuiSvgIcon-root": {
                  fontSize: "12px",
                },
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );
  }
);

GapIndicator.displayName = "GapIndicator";
