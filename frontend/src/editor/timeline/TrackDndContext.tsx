import React, {
  useCallback,
  useState,
  createContext,
  useRef,
  useEffect,
  useMemo,
} from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragMoveEvent,
  MeasuringStrategy,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { StoreContext } from "../../store";
import { useContext } from "react";
import { EditorElement, Track, TrackType } from "../../types";

// 拖拽方向类型
type DragDirection = "up" | "down" | "none";

// 拖拽激活配置常量
const DRAG_ACTIVATION_DISTANCE = 10; // 激活拖拽所需的最小移动距离（像素）
const DRAG_TOLERANCE = 10; // 拖拽容差（像素）
const DRAG_DELAY = 10; // 拖拽延迟（毫秒）

// 泳道间隔类型
interface TrackGap {
  id: string;
  beforeTrackId: string | null;
  afterTrackId: string | null;
  trackType: TrackType;
}

// 创建拖拽上下文
interface TrackDndContextValue {
  activeElement: EditorElement | null;
  activeTrack: Track | null;
  overTrack: Track | null;
  overGap: TrackGap | null;
  isCreatingNewTrack: boolean;
}

// 拖拽状态类型
interface DragState {
  activeElement: EditorElement | null;
  activeTrack: Track | null;
  overTrack: Track | null;
  overGap: TrackGap | null;
  isCreatingNewTrack: boolean;
  dragDirection: DragDirection;
}

export const TrackDndStateContext = createContext<TrackDndContextValue>({
  activeElement: null,
  activeTrack: null,
  overTrack: null,
  overGap: null,
  isCreatingNewTrack: false,
});

// 导出上下文钩子
export const useTrackDnd = () => useContext(TrackDndStateContext);

interface TrackDndContextProps {
  children: React.ReactNode;
}

export const TrackDndContext: React.FC<TrackDndContextProps> = ({
  children,
}) => {
  const store = useContext(StoreContext);

  // 合并相关的拖拽状态，减少状态更新触发的重渲染
  const [dragState, setDragState] = useState<DragState>({
    activeElement: null,
    activeTrack: null,
    overTrack: null,
    overGap: null,
    isCreatingNewTrack: false,
    dragDirection: "none",
  });

  // 引用存储所有轨道间隔元素
  const trackGapsRef = useRef<TrackGap[]>([]);
  const initialDragPositionRef = useRef<{ x: number; y: number } | null>(null);

  // 使用 useMemo 优化轨道间隔计算
  const elementTypeFromActive = useMemo(() => {
    if (!dragState.activeElement) return null;

    // 获取当前元素类型
    if (
      dragState.activeElement.type === "image" ||
      dragState.activeElement.type === "video"
    ) {
      return "media" as TrackType;
    }
    return dragState.activeElement.type as TrackType;
  }, [dragState.activeElement]);

  // 生成轨道间隔，使用 useMemo 优化性能
  useEffect(() => {
    if (!dragState.activeElement || !elementTypeFromActive) {
      trackGapsRef.current = [];
      return;
    }

    // 获取所有轨道
    const tracks = store.trackManager.tracks;
    if (!tracks || tracks.length === 0) return;

    const elementType = elementTypeFromActive;
    const gaps: TrackGap[] = [];

    // 添加第一个轨道之前的间隔
    gaps.push({
      id: `gap-before-${tracks[0].id}`,
      beforeTrackId: null,
      afterTrackId: tracks[0].id,
      trackType: elementType,
    });

    // 添加轨道之间的间隔
    for (let i = 0; i < tracks.length - 1; i++) {
      gaps.push({
        id: `gap-between-${tracks[i].id}-${tracks[i + 1].id}`,
        beforeTrackId: tracks[i].id,
        afterTrackId: tracks[i + 1].id,
        trackType: elementType,
      });
    }

    // 添加最后一个轨道之后的间隔
    gaps.push({
      id: `gap-after-${tracks[tracks.length - 1].id}`,
      beforeTrackId: tracks[tracks.length - 1].id,
      afterTrackId: null,
      trackType: elementType,
    });

    trackGapsRef.current = gaps;
  }, [
    dragState.activeElement,
    store.trackManager.tracks,
    elementTypeFromActive,
  ]);

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // 优化拖拽激活配置
      activationConstraint: {
        distance: DRAG_ACTIVATION_DISTANCE,
        tolerance: DRAG_TOLERANCE,
        delay: DRAG_DELAY,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // 创建一个更新拖拽状态的工具函数，减少重复代码
  const updateDragState = useCallback((newState: Partial<DragState>) => {
    setDragState((prev) => ({ ...prev, ...newState }));
  }, []);

  // 处理拖拽开始
  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      const elementId = active.id as string;

      // 查找被拖拽的元素
      const element = store.editorElements.find((el) => el.id === elementId);
      if (!element) return;

      // 查找元素所在的轨道
      const track = store.trackManager.getTrackByElementId(elementId);
      if (!track) return;

      // 使用合并后的状态更新函数
      updateDragState({
        activeElement: element,
        activeTrack: track,
        dragDirection: "none",
      });

      // 记录初始拖拽位置
      initialDragPositionRef.current = { x: 0, y: 0 };

      // 添加全局拖拽样式
      document.body.classList.add("track-dragging-active");

      // 如果正在播放，暂停播放
      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [store, updateDragState]
  );

  // 处理拖拽移动
  const handleDragMove = useCallback(
    (event: DragMoveEvent) => {
      const { over } = event;

      if (!over) {
        updateDragState({
          overTrack: null,
          overGap: null,
          isCreatingNewTrack: false,
        });
        return;
      }

      const overId = over.id as string;

      // 计算拖拽方向
      if (initialDragPositionRef.current && event.delta) {
        const deltaY = event.delta.y;
        if (Math.abs(deltaY) > 10) {
          // 设置一个阈值，避免轻微移动就改变方向
          updateDragState({
            dragDirection: deltaY > 0 ? "down" : "up",
          });
        }
      }

      // 检查是否悬停在轨道上
      if (overId.startsWith("track-")) {
        const id = overId.replace("track-", "");
        const track = store.trackManager.tracks.find((t) => t.id === id);
        if (track) {
          updateDragState({
            overTrack: track,
            overGap: null,
            isCreatingNewTrack: false,
          });
        }
      }
      // 检查是否悬停在轨道间隔上
      else if (overId.startsWith("gap-")) {
        // 查找匹配的间隔
        const gap = trackGapsRef.current.find((g) => g.id === overId);
        if (gap) {
          updateDragState({
            overTrack: null,
            overGap: gap,
            isCreatingNewTrack: true,
          });

          // 添加高亮样式到对应的间隔元素
          const gapElement = document.getElementById(overId);
          if (gapElement) {
            gapElement.classList.add("track-gap-highlight");
          }
        }
      } else {
        updateDragState({
          overTrack: null,
          overGap: null,
          isCreatingNewTrack: false,
        });
      }

      // 移除所有其他间隔的高亮样式
      trackGapsRef.current.forEach((gap) => {
        if (gap.id !== overId) {
          const gapElement = document.getElementById(gap.id);
          if (gapElement) {
            gapElement.classList.remove("track-gap-highlight");
          }
        }
      });
    },
    [store.trackManager.tracks, updateDragState]
  );

  // 获取要创建的轨道类型
  const getTrackTypeForElement = useCallback(
    (element: EditorElement): TrackType => {
      if (element.type === "image" || element.type === "video") {
        return "media";
      }
      return element.type as TrackType;
    },
    []
  );

  // 清理拖拽状态的通用函数
  const cleanupDragState = useCallback(() => {
    // 移除全局拖拽样式
    document.body.classList.remove("track-dragging-active");

    // 清理所有轨道间隙的高亮样式
    trackGapsRef.current.forEach((gap) => {
      const gapElement = document.getElementById(gap.id);
      if (gapElement) {
        gapElement.classList.remove("track-gap-highlight");
      }
    });

    // 重置状态
    updateDragState({
      activeElement: null,
      activeTrack: null,
      overTrack: null,
      overGap: null,
      isCreatingNewTrack: false,
      dragDirection: "none",
    });
    initialDragPositionRef.current = null;
  }, [updateDragState]);

  // 处理拖拽取消
  const handleDragCancel = useCallback(() => {
    cleanupDragState();
  }, [cleanupDragState]);

  // 处理拖拽结束
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (!active || !over || !dragState.activeElement) {
        cleanupDragState();
        return;
      }

      const elementId = active.id as string;
      const overId = over.id.toString();
      const elementType = getTrackTypeForElement(dragState.activeElement);

      // 检查是否放置在轨道上
      if (overId.startsWith("track-")) {
        const targetTrackId = overId.replace("track-", "");
        const targetTrack = store.trackManager.tracks.find(
          (t) => t.id === targetTrackId
        );

        // 如果目标轨道与原轨道不同，则处理元素移动
        if (
          dragState.activeTrack &&
          dragState.activeTrack.id !== targetTrackId &&
          targetTrack
        ) {
          // 检查元素类型是否与目标轨道类型匹配
          let isTypeMatch = false;

          // 对于image和video类型的元素，它们可以放在media类型的轨道中
          if (
            (dragState.activeElement.type === "image" ||
              dragState.activeElement.type === "video") &&
            targetTrack.type === "media"
          ) {
            isTypeMatch = true;
          } else {
            isTypeMatch = elementType === targetTrack.type;
          }

          if (isTypeMatch) {
            // 类型匹配，直接移动元素到目标轨道
            store.trackManager.moveElementToTrack(elementId, targetTrackId);
          } else {
            // 类型不匹配，根据拖拽方向创建新轨道
            const targetTrackIndex = store.trackManager.tracks.findIndex(
              (t) => t.id === targetTrackId
            );

            if (targetTrackIndex !== -1) {
              let newTrackPosition = targetTrackIndex;

              // 根据拖拽方向确定新轨道的位置
              if (dragState.dragDirection === "down") {
                // 向下拖动，在目标轨道下方创建新轨道
                newTrackPosition = targetTrackIndex + 1;
              } else if (dragState.dragDirection === "up") {
                // 向上拖动，在目标轨道上方创建新轨道
                newTrackPosition = targetTrackIndex;
              }

              const newTrackType = elementType;

              // 创建新轨道并将元素移动到新轨道
              const newTrack = store.trackManager.createTrackAtPosition(
                newTrackType,
                newTrackPosition,
                `${
                  newTrackType.charAt(0).toUpperCase() + newTrackType.slice(1)
                } Track ${store.trackManager.getTrackCountByType(newTrackType)}`
              );

              // 将元素移动到新轨道
              store.trackManager.moveElementToTrack(elementId, newTrack.id);
            } else {
              // 如果找不到目标轨道索引，直接移动到目标轨道
              store.trackManager.moveElementToTrack(elementId, targetTrackId);
            }
          }

          // 保存更改
          store.saveChange();
        }
      }
      // 检查是否放置在轨道间隔上，需要创建新轨道
      else if (
        overId.startsWith("gap-") &&
        dragState.isCreatingNewTrack &&
        dragState.overGap
      ) {
        // 创建新轨道
        let newTrackPosition = -1;

        // 确定新轨道的位置
        if (dragState.overGap.beforeTrackId && dragState.overGap.afterTrackId) {
          // 在两个轨道之间插入
          const beforeIndex = store.trackManager.tracks.findIndex(
            (t) => t.id === dragState.overGap.beforeTrackId
          );

          if (beforeIndex !== -1) {
            newTrackPosition = beforeIndex + 1;
          }
        } else if (dragState.overGap.beforeTrackId) {
          // 在最后一个轨道之后插入
          const beforeIndex = store.trackManager.tracks.findIndex(
            (t) => t.id === dragState.overGap.beforeTrackId
          );

          if (beforeIndex !== -1) {
            newTrackPosition = beforeIndex + 1;
          }
        } else if (dragState.overGap.afterTrackId) {
          // 在第一个轨道之前插入
          newTrackPosition = 0;
        }

        const newTrackType = elementType;

        // 创建新轨道并将元素移动到新轨道
        const newTrack = store.trackManager.createTrackAtPosition(
          newTrackType,
          newTrackPosition
        );

        // 将元素移动到新轨道
        store.trackManager.moveElementToTrack(elementId, newTrack.id);

        // 保存更改
        store.saveChange();
      }

      // 检查并删除所有空轨道
      store.trackManager.removeEmptyTracks();

      // 根据轨道顺序更新Canvas上的元素显示顺序
      store.updateCanvasOrderByTrackOrder();

      // 清理拖拽状态
      cleanupDragState();
    },
    [
      dragState,
      store,
      updateDragState,
      getTrackTypeForElement,
      cleanupDragState,
    ]
  );

  // 创建上下文值
  const contextValue = useMemo(
    () => ({
      activeElement: dragState.activeElement,
      activeTrack: dragState.activeTrack,
      overTrack: dragState.overTrack,
      overGap: dragState.overGap,
      isCreatingNewTrack: dragState.isCreatingNewTrack,
    }),
    [dragState]
  );

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
      modifiers={[restrictToVerticalAxis]}
      measuring={{
        droppable: {
          strategy: MeasuringStrategy.Always,
        },
      }}
    >
      <TrackDndStateContext.Provider value={contextValue}>
        {children}
      </TrackDndStateContext.Provider>
    </DndContext>
  );
};
